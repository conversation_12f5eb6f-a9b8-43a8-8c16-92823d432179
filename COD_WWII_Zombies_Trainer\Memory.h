#pragma once
#include <Windows.h>
#include <TlHelp32.h>
#include <vector>
#include <string>

class Memory {
private:
    HANDLE processHandle;
    DWORD processId;
    uintptr_t moduleBase;
    
public:
    Memory();
    ~Memory();
    
    // Process management
    bool AttachToProcess(const std::wstring& processName);
    void DetachFromProcess();
    bool IsProcessValid();
    
    // Memory operations
    template<typename T>
    T Read(uintptr_t address);
    
    template<typename T>
    bool Write(uintptr_t address, T value);
    
    bool ReadBytes(uintptr_t address, void* buffer, size_t size);
    bool WriteBytes(uintptr_t address, const void* buffer, size_t size);
    
    // Address resolution
    uintptr_t GetModuleBase() const { return moduleBase; }
    uintptr_t ResolvePointer(uintptr_t baseAddress, const std::vector<uintptr_t>& offsets);
    
    // Pattern scanning
    uintptr_t PatternScan(const std::string& pattern, const std::string& mask);
    uintptr_t PatternScanModule(const std::string& pattern, const std::string& mask, const std::wstring& moduleName);
    
    // Utility functions
    DWORD GetProcessId(const std::wstring& processName);
    uintptr_t GetModuleBaseAddress(const std::wstring& moduleName, DWORD processId);
    
    // Cheat Engine bypass
    bool BypassCheatEngineDetection();
    
private:
    bool SetDebugPrivileges();
};

// Template implementations
template<typename T>
T Memory::Read(uintptr_t address) {
    T value = {};
    ReadProcessMemory(processHandle, reinterpret_cast<LPCVOID>(address), &value, sizeof(T), nullptr);
    return value;
}

template<typename T>
bool Memory::Write(uintptr_t address, T value) {
    return WriteProcessMemory(processHandle, reinterpret_cast<LPVOID>(address), &value, sizeof(T), nullptr);
}
