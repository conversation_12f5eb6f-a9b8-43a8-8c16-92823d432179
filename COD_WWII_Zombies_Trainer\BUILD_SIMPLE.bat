@echo off
title COD WWII Simple Trainer Build
color 0B

echo.
echo ========================================
echo COD WWII Simple Trainer Build
echo Based on UnknownCheats Research
echo ========================================
echo.

REM Check admin
net session >nul 2>&1
if %errorLevel% neq 0 (
    color 0C
    echo ERROR: Must run as Administrator!
    echo Right-click and select "Run as administrator"
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo ✓ Running as Administrator

REM Find Visual Studio
set "VS2022=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
set "VS2019=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"

if exist "%VS2022%" (
    echo ✓ Found Visual Studio 2022
    call "%VS2022%" >nul 2>&1
) else if exist "%VS2019%" (
    echo ✓ Found Visual Studio 2019
    call "%VS2019%" >nul 2>&1
) else (
    color 0C
    echo ✗ Visual Studio not found!
    echo.
    echo Please install Visual Studio 2019 or 2022
    echo Make sure to include "Desktop development with C++"
    echo Download: https://visualstudio.microsoft.com/downloads/
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

REM Create output directory
if not exist "bin" mkdir bin

echo ✓ Compiling simple trainer...
echo.

REM Compile the simple trainer
cl.exe /EHsc /O2 /MT SimpleTrainer.cpp /Fe:bin\SimpleTrainer.exe /link kernel32.lib user32.lib psapi.lib xinput.lib

if %errorLevel% equ 0 (
    color 0A
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo ✓ Simple trainer created: bin\SimpleTrainer.exe
    echo ✓ Based on proven UnknownCheats addresses
    echo ✓ Xbox controller support included
    echo.
    echo FEATURES:
    echo • God Mode (Key: 1)
    echo • Infinite Ammo (Key: 2)  
    echo • Rapid Fire (Key: 3)
    echo • Aimbot with Xbox controller (Key: 4)
    echo • Infinite Points (Key: 5)
    echo • All Perks (Key: 6)
    echo • Aimbot strength adjustment (+/-)
    echo.
    echo XBOX CONTROLLER:
    echo • LT (Left Trigger) = Activate aimbot
    echo • RT (Right Trigger) = Auto-fire
    echo • Controller vibrates when aimbot active
    echo.
    echo USAGE:
    echo 1. Launch COD WWII Zombies
    echo 2. Right-click bin\SimpleTrainer.exe
    echo 3. Select "Run as administrator"
    echo 4. Use number keys to toggle features
    echo 5. Use Xbox controller LT/RT for aimbot
    echo.
) else (
    color 0C
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo.
    echo Check that Visual Studio C++ tools are installed.
    echo.
)

echo Press any key to close...
pause >nul
