@echo off
title COD WWII Trainer - MinGW Build
color 0B

echo.
echo ========================================
echo COD WWII Trainer - MinGW Build
echo No Visual Studio Required
echo ========================================
echo.

REM Check admin
net session >nul 2>&1
if %errorLevel% neq 0 (
    color 0C
    echo ERROR: Must run as Administrator!
    echo Right-click and select "Run as administrator"
    pause >nul
    exit /b 1
)

echo ✓ Running as Administrator

REM Check if MinGW is available
where gcc >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ Found GCC compiler
    goto :compile
)

REM Try to find MinGW in common locations
if exist "C:\MinGW\bin\gcc.exe" (
    echo ✓ Found MinGW in C:\MinGW
    set PATH=C:\MinGW\bin;%PATH%
    goto :compile
)

if exist "C:\msys64\mingw64\bin\gcc.exe" (
    echo ✓ Found MinGW in MSYS2
    set PATH=C:\msys64\mingw64\bin;%PATH%
    goto :compile
)

REM MinGW not found - offer to download
color 0E
echo ⚠ MinGW compiler not found
echo.
echo OPTION 1: Install MinGW
echo 1. Download from: https://www.mingw-w64.org/downloads/
echo 2. Install to C:\MinGW
echo 3. Run this script again
echo.
echo OPTION 2: Install Visual Studio (easier)
echo 1. Download Visual Studio Community 2022 (free)
echo 2. Include "Desktop development with C++"
echo 3. Use BUILD_SIMPLE.bat instead
echo.
echo Press any key to exit...
pause >nul
exit /b 1

:compile
if not exist "bin" mkdir bin

echo ✓ Compiling with GCC...
echo.

REM Compile with MinGW
gcc -O2 -std=c++17 SimpleTrainer.cpp -o bin\SimpleTrainer.exe -lkernel32 -luser32 -lpsapi -lxinput -static-libgcc -static-libstdc++

if %errorLevel% equ 0 (
    color 0A
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo ✓ Trainer created: bin\SimpleTrainer.exe
    echo ✓ No Visual Studio required!
    echo.
    echo READY TO USE:
    echo 1. Launch COD WWII Zombies
    echo 2. Right-click bin\SimpleTrainer.exe
    echo 3. Select "Run as administrator"
    echo 4. Use number keys + Xbox controller
    echo.
) else (
    color 0C
    echo.
    echo BUILD FAILED with MinGW!
    echo Try installing Visual Studio instead.
    echo.
)

echo Press any key to close...
pause >nul
