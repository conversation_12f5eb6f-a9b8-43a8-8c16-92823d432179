@echo off
echo ========================================
echo COD WWII Zombies Trainer Build Script
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click on build.bat and select "Run as administrator"
    pause
    exit /b 1
)

REM Check for Visual Studio installation
echo Checking for Visual Studio installation...
where cl >nul 2>&1
if %errorLevel% neq 0 (
    echo Setting up Visual Studio environment...
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if %errorLevel% neq 0 (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if %errorLevel% neq 0 (
            echo ERROR: Visual Studio not found!
            echo Please install Visual Studio 2019 or 2022 with C++ development tools.
            echo Download from: https://visualstudio.microsoft.com/downloads/
            pause
            exit /b 1
        )
    )
)

REM Check for CMake
echo Checking for CMake...
where cmake >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: CMake not found!
    echo Please install CMake from: https://cmake.org/download/
    pause
    exit /b 1
)

REM Create build directory
if not exist "build" mkdir build
cd build

echo Configuring project with CMake...
cmake .. -G "Visual Studio 17 2022" -A x64
if %errorLevel% neq 0 (
    echo Trying Visual Studio 2019...
    cmake .. -G "Visual Studio 16 2019" -A x64
    if %errorLevel% neq 0 (
        echo ERROR: CMake configuration failed!
        echo Make sure you have Visual Studio 2019/2022 installed with C++ development tools.
        pause
        exit /b 1
    )
)

echo.
echo Building project in Release mode...
cmake --build . --config Release --parallel
if %errorLevel% neq 0 (
    echo ERROR: Build failed!
    echo Check the error messages above for details.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Build completed successfully!
echo ========================================
echo.
echo Executable location: build\bin\Release\COD_WWII_Zombies_Trainer.exe
echo.
echo IMPORTANT SETUP INSTRUCTIONS:
echo 1. Connect your Xbox controller to PC (USB or Bluetooth)
echo 2. Launch Call of Duty WWII (Steam version)
echo 3. Enter Zombies mode
echo 4. Run the trainer as Administrator
echo 5. Look for "Xbox Controller detected" message
echo.
echo XBOX CONTROLLER CONTROLS:
echo - LT (Left Trigger): Activate Aimbot
echo - RT (Right Trigger): Auto-Fire
echo - D-Pad: Toggle core features
echo - Face buttons: Utility functions
echo.
echo The trainer is optimized for Xbox controller with:
echo - Enhanced aimbot (90° FOV)
echo - Auto-fire system
echo - Haptic feedback
echo - Increased fire rate
echo.
pause
