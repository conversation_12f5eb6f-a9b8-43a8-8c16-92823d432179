@echo off
title COD WWII Zombies Trainer Launcher
color 0B

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                COD WWII ZOMBIES ULTIMATE TRAINER             ║
echo  ║                        Launcher                              ║
echo  ║                   Xbox Controller Ready                     ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    color 0C
    echo  [ERROR] Must run as Administrator!
    echo.
    echo  Right-click on LAUNCH_TRAINER.bat and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Check if trainer exists
if exist "build\bin\Release\COD_WWII_Zombies_Trainer.exe" (
    echo  ✓ Trainer found
) else (
    echo  ⚠ Trainer not built yet
    echo.
    echo  Building trainer first...
    call QUICK_SETUP.bat
    if %errorLevel% neq 0 (
        echo  Build failed! Check setup script.
        pause
        exit /b 1
    )
)

echo  [CHECKING] Xbox Controller...
powershell -Command "Get-PnpDevice -Class 'HIDClass' | Where-Object {$_.FriendlyName -like '*Xbox*' -or $_.FriendlyName -like '*Controller*'}" >nul 2>&1
if %errorLevel% equ 0 (
    echo  ✓ Xbox controller detected
) else (
    color 0E
    echo  ⚠ Xbox controller not detected
    echo.
    echo  Please connect your Xbox controller and try again.
    echo  The trainer will work with keyboard, but controller is recommended.
    echo.
    set /p continue="Continue anyway? (y/n): "
    if /i not "%continue%"=="y" (
        echo  Exiting...
        pause
        exit /b 0
    )
)

echo  [CHECKING] Call of Duty WWII...
tasklist /FI "IMAGENAME eq s2_mp64_ship.exe" 2>NUL | find /I /N "s2_mp64_ship.exe">NUL
if %errorLevel% equ 0 (
    echo  ✓ COD WWII is running
) else (
    color 0E
    echo  ⚠ COD WWII not running
    echo.
    echo  Please launch Call of Duty WWII and enter Zombies mode first.
    echo.
    set /p continue="Continue anyway? (y/n): "
    if /i not "%continue%"=="y" (
        echo  Exiting...
        pause
        exit /b 0
    )
)

echo.
color 0A
echo  🚀 LAUNCHING TRAINER...
echo.
echo  📋 QUICK REFERENCE:
echo.
echo  🎮 XBOX CONTROLLER:
echo  • LT + RT = Aimbot + Auto-Fire
echo  • D-Pad = Core features
echo  • Face buttons = Utilities
echo.
echo  ⌨️ KEYBOARD BACKUP:
echo  • F1-F12 = All features
echo  • INSERT/DELETE = Movement
echo.

cd build\bin\Release
start COD_WWII_Zombies_Trainer.exe

echo  ✓ Trainer launched!
echo.
echo  Check the trainer console window for status and controls.
echo  The trainer will automatically detect your Xbox controller.
echo.
echo  Press any key to close this launcher...
pause >nul
