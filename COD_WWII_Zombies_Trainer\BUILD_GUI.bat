@echo off
title Build GUI Trainer
color 0B

echo.
echo ========================================
echo Building GUI Trainer
echo ========================================
echo.

cl.exe /EHsc /O2 /MT GUITrainer.cpp /Fe:GUITrainer.exe /link kernel32.lib user32.lib xinput.lib comctl32.lib

if exist "GUITrainer.exe" (
    color 0A
    echo.
    echo ✓ SUCCESS! GUITrainer.exe created
    echo.
    echo This version has a proper GUI with:
    echo • Buttons to toggle features
    echo • Slider for aimbot strength
    echo • Visual feedback
    echo.
    echo USAGE:
    echo 1. Get into a Zombies match
    echo 2. Right-click GUITrainer.exe
    echo 3. Select "Run as administrator"
    echo 4. Use the GUI buttons and sliders
    echo.
) else (
    color 0C
    echo.
    echo ✗ BUILD FAILED!
    echo.
)

pause
