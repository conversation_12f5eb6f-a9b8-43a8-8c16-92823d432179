@echo off
title COD WWII Zombies Trainer - Quick Setup
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                COD WWII ZOMBIES ULTIMATE TRAINER             ║
echo  ║                        Quick Setup                           ║
echo  ║                   Xbox Controller Optimized                 ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    color 0C
    echo  [ERROR] This script must be run as Administrator!
    echo.
    echo  Right-click on QUICK_SETUP.bat and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo  [STEP 1] Checking system requirements...
echo.

REM Check Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
if "%version%" == "10.0" (
    echo  ✓ Windows 10/11 detected
) else (
    echo  ⚠ Warning: Windows 10/11 recommended
)

REM Check for Xbox controller
echo  [STEP 2] Checking for Xbox controller...
powershell -Command "Get-PnpDevice -Class 'HIDClass' | Where-Object {$_.FriendlyName -like '*Xbox*' -or $_.FriendlyName -like '*Controller*'}" >nul 2>&1
if %errorLevel% equ 0 (
    echo  ✓ Xbox controller detected
) else (
    echo  ⚠ Xbox controller not detected - connect controller and try again
)

REM Check for COD WWII
echo  [STEP 3] Checking for Call of Duty WWII...
if exist "s2_mp64_ship.exe" (
    echo  ✓ COD WWII found in current directory
) else (
    if exist "C:\Program Files (x86)\Steam\steamapps\common\Call of Duty WWII\s2_mp64_ship.exe" (
        echo  ✓ COD WWII found in Steam directory
    ) else (
        echo  ⚠ COD WWII not found - make sure Steam version is installed
    )
)

echo.
echo  [STEP 4] Building trainer...
echo.

REM Build the trainer
call build.bat
if %errorLevel% neq 0 (
    color 0C
    echo  [ERROR] Build failed! Check build.bat output for details.
    pause
    exit /b 1
)

echo.
color 0B
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                        SETUP COMPLETE!                      ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.
echo  🎮 XBOX CONTROLLER READY!
echo.
echo  📋 QUICK START GUIDE:
echo  1. Connect Xbox controller to PC
echo  2. Launch Call of Duty WWII (Steam)
echo  3. Enter Zombies mode
echo  4. Run trainer as Administrator
echo.
echo  🎯 CONTROLLER CONTROLS:
echo  • LT (Left Trigger)  - Activate Aimbot
echo  • RT (Right Trigger) - Auto-Fire
echo  • D-Pad Up/Down/Left/Right - Core features
echo  • Face buttons (A/B/X/Y) - Utilities
echo  • LB/RB - Advanced features
echo.
echo  🚀 ENHANCED FEATURES:
echo  • 90° FOV aimbot (optimized for controller)
echo  • Auto-fire system with ultra-fast rate
echo  • Haptic feedback when targets locked
echo  • No fall damage + Super jump
echo  • All features accessible via controller
echo.
echo  📁 Trainer location: build\bin\Release\COD_WWII_Zombies_Trainer.exe
echo.

REM Ask if user wants to launch trainer now
set /p launch="Launch trainer now? (y/n): "
if /i "%launch%"=="y" (
    echo.
    echo  Launching trainer...
    cd build\bin\Release
    start COD_WWII_Zombies_Trainer.exe
    echo  Trainer launched! Check the console window for status.
)

echo.
echo  Press any key to exit setup...
pause >nul
