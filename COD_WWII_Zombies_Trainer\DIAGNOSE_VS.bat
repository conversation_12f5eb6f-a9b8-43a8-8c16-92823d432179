@echo off
title Visual Studio Diagnostic
color 0B

echo.
echo ========================================
echo Visual Studio Diagnostic
echo ========================================
echo.

REM Check admin
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Must run as Administrator!
    pause >nul
    exit /b 1
)

echo ✓ Running as Administrator
echo.

echo [CHECKING] Visual Studio Installations:
echo.

REM Check all possible VS locations
set FOUND_VS=0

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo ✓ Found: Visual Studio 2022 Community
    set FOUND_VS=1
    set VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    echo ✓ Found: Visual Studio 2022 Professional
    set FOUND_VS=1
    set VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat" (
    echo ✓ Found: Visual Studio 2022 Enterprise
    set FOUND_VS=1
    set VS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo ✓ Found: Visual Studio 2019 Community
    set FOUND_VS=1
    set VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    echo ✓ Found: Visual Studio 2019 Professional
    set FOUND_VS=1
    set VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat
)

REM Check Build Tools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    echo ✓ Found: Visual Studio 2022 Build Tools
    set FOUND_VS=1
    set VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    echo ✓ Found: Visual Studio 2019 Build Tools
    set FOUND_VS=1
    set VS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars64.bat
)

if %FOUND_VS%==0 (
    color 0C
    echo ✗ No Visual Studio installations found!
    echo.
    echo Please install Visual Studio Community 2022 with C++ tools
    echo Download: https://visualstudio.microsoft.com/downloads/
    pause >nul
    exit /b 1
)

echo.
echo [TESTING] Setting up Visual Studio environment...
echo Using: %VS_PATH%
echo.

call "%VS_PATH%"

echo.
echo [TESTING] Checking if cl.exe is available...
where cl.exe >nul 2>&1
if %errorLevel% equ 0 (
    echo ✓ cl.exe found and working
    cl.exe 2>&1 | findstr "Microsoft"
) else (
    color 0C
    echo ✗ cl.exe not found after setting up environment
    echo.
    echo This means C++ development tools are not installed.
    echo.
    echo SOLUTION:
    echo 1. Open Visual Studio Installer
    echo 2. Click "Modify" on your Visual Studio installation
    echo 3. Make sure "Desktop development with C++" is checked
    echo 4. Click "Modify" to install missing components
    echo.
    pause >nul
    exit /b 1
)

echo.
echo [TESTING] Checking required libraries...
if exist "%WindowsSdkDir%\Lib\*\um\x64\kernel32.lib" (
    echo ✓ Windows SDK libraries found
) else (
    echo ⚠ Windows SDK libraries may be missing
)

echo.
echo ========================================
echo DIAGNOSIS COMPLETE
echo ========================================
echo.

if %FOUND_VS%==1 (
    color 0A
    echo ✓ Visual Studio is properly installed and configured
    echo ✓ Ready to build the trainer
    echo.
    echo Next step: Run BUILD_SIMPLE.bat again
    echo.
) else (
    color 0C
    echo ✗ Issues found with Visual Studio setup
    echo Check the messages above for solutions
    echo.
)

echo Press any key to close...
pause >nul
