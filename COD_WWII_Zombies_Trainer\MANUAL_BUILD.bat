@echo off
title Manual Build - All Methods
color 0B

echo.
echo ========================================
echo Manual Build - Trying All Methods
echo ========================================
echo.

REM Check admin
net session >nul 2>&1
if %errorLevel% neq 0 (
    color 0C
    echo ERROR: Must run as Administrator!
    pause >nul
    exit /b 1
)

echo ✓ Running as Administrator
echo.

if not exist "bin" mkdir bin

echo [METHOD 1] Trying Visual Studio 2022 Community...
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    cl.exe /EHsc /O2 /MT SimpleTrainer.cpp /Fe:bin\SimpleTrainer.exe /link kernel32.lib user32.lib psapi.lib xinput.lib >nul 2>&1
    if exist "bin\SimpleTrainer.exe" (
        color 0A
        echo ✓ SUCCESS with VS 2022 Community!
        goto :success
    )
)

echo [METHOD 2] Trying Visual Studio 2022 Professional...
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    cl.exe /EHsc /O2 /MT SimpleTrainer.cpp /Fe:bin\SimpleTrainer.exe /link kernel32.lib user32.lib psapi.lib xinput.lib >nul 2>&1
    if exist "bin\SimpleTrainer.exe" (
        color 0A
        echo ✓ SUCCESS with VS 2022 Professional!
        goto :success
    )
)

echo [METHOD 3] Trying Visual Studio 2019...
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    cl.exe /EHsc /O2 /MT SimpleTrainer.cpp /Fe:bin\SimpleTrainer.exe /link kernel32.lib user32.lib psapi.lib xinput.lib >nul 2>&1
    if exist "bin\SimpleTrainer.exe" (
        color 0A
        echo ✓ SUCCESS with VS 2019!
        goto :success
    )
)

echo [METHOD 4] Trying Build Tools...
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    cl.exe /EHsc /O2 /MT SimpleTrainer.cpp /Fe:bin\SimpleTrainer.exe /link kernel32.lib user32.lib psapi.lib xinput.lib >nul 2>&1
    if exist "bin\SimpleTrainer.exe" (
        color 0A
        echo ✓ SUCCESS with Build Tools!
        goto :success
    )
)

echo [METHOD 5] Trying Developer Command Prompt...
where cl.exe >nul 2>&1
if %errorLevel% equ 0 (
    cl.exe /EHsc /O2 /MT SimpleTrainer.cpp /Fe:bin\SimpleTrainer.exe /link kernel32.lib user32.lib psapi.lib xinput.lib >nul 2>&1
    if exist "bin\SimpleTrainer.exe" (
        color 0A
        echo ✓ SUCCESS with existing environment!
        goto :success
    )
)

echo [METHOD 6] Trying MinGW/GCC...
where gcc >nul 2>&1
if %errorLevel% equ 0 (
    gcc -O2 -std=c++17 SimpleTrainer.cpp -o bin\SimpleTrainer.exe -lkernel32 -luser32 -lpsapi -lxinput -static-libgcc -static-libstdc++ >nul 2>&1
    if exist "bin\SimpleTrainer.exe" (
        color 0A
        echo ✓ SUCCESS with MinGW!
        goto :success
    )
)

REM All methods failed
color 0C
echo.
echo ========================================
echo ALL BUILD METHODS FAILED
echo ========================================
echo.
echo Possible issues:
echo 1. C++ development tools not installed in Visual Studio
echo 2. Windows SDK missing
echo 3. Source file issues
echo.
echo SOLUTIONS:
echo 1. Run DIAGNOSE_VS.bat to check your VS installation
echo 2. Open Visual Studio Installer and add "Desktop development with C++"
echo 3. Install Windows 10/11 SDK
echo.
echo Or try downloading a pre-built trainer from UnknownCheats forum
echo.
goto :end

:success
echo.
echo ========================================
echo BUILD SUCCESSFUL!
echo ========================================
echo.
echo ✓ Trainer created: bin\SimpleTrainer.exe
echo.
echo USAGE:
echo 1. Launch COD WWII Zombies
echo 2. Right-click bin\SimpleTrainer.exe
echo 3. Select "Run as administrator"
echo 4. Use number keys to toggle features
echo 5. Use Xbox controller LT/RT for aimbot
echo.
echo CONTROLS:
echo • 1 = God Mode
echo • 2 = Infinite Ammo
echo • 3 = Rapid Fire
echo • 4 = Aimbot
echo • 5 = Infinite Points
echo • 6 = All Perks
echo • +/- = Aimbot strength
echo • ESC = Exit
echo.
echo XBOX CONTROLLER:
echo • LT = Activate aimbot
echo • RT = Auto-fire
echo • Vibrates when active
echo.

:end
echo Press any key to close...
pause >nul
