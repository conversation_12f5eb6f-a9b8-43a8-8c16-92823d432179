@echo off
title Rebuild Trainer
color 0B

echo.
echo ========================================
echo Rebuilding Updated Trainer
echo ========================================
echo.

REM Delete old versions
if exist "SimpleTrainer.exe" del "SimpleTrainer.exe"
if exist "SimpleTrainer_v2.exe" del "SimpleTrainer_v2.exe"

echo ✓ Compiling updated trainer...

cl.exe /EHsc /O2 /MT SimpleTrainer.cpp /Fe:SimpleTrainer_v2.exe /link kernel32.lib user32.lib psapi.lib xinput.lib

if exist "SimpleTrainer_v2.exe" (
    color 0A
    echo.
    echo ========================================
    echo SUCCESS!
    echo ========================================
    echo.
    echo ✓ Updated trainer created: SimpleTrainer_v2.exe
    echo.
    echo IMPORTANT: You must be IN a Zombies match!
    echo 1. Launch COD WWII
    echo 2. Go to Zombies mode
    echo 3. START a match (any map)
    echo 4. Wait until you're playing
    echo 5. Alt+Tab out
    echo 6. Right-click SimpleTrainer_v2.exe
    echo 7. Select "Run as administrator"
    echo.
) else (
    color 0C
    echo.
    echo BUILD FAILED!
    echo Make sure you're in the Developer Command Prompt.
    echo.
)

echo Press any key to close...
pause >nul
