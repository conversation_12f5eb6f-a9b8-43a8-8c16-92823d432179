@echo off
title COD WWII Zombies Trainer - Status Check
color 0B
echo.
echo ========================================
echo COD WWII Zombies Trainer - Status Check
echo ========================================
echo.

echo [CHECKING] Files in current directory:
echo.
dir /b *.cpp *.h *.bat 2>nul
echo.

echo [CHECKING] Build output:
if exist "bin\COD_WWII_Zombies_Trainer.exe" (
    color 0A
    echo ✓ SUCCESS: Trainer executable found!
    echo   Location: bin\COD_WWII_Zombies_Trainer.exe
    echo   Size: 
    dir "bin\COD_WWII_Zombies_Trainer.exe" | find "COD_WWII_Zombies_Trainer.exe"
    echo.
    echo ✓ READY TO USE!
    echo.
    echo NEXT STEPS:
    echo 1. Connect Xbox controller to PC
    echo 2. Launch Call of Duty WWII Zombies
    echo 3. Right-click bin\COD_WWII_Zombies_Trainer.exe
    echo 4. Select "Run as administrator"
    echo.
) else (
    color 0E
    echo ⚠ Trainer not built yet
    echo.
    echo TO BUILD:
    echo 1. Right-click DIRECT_BUILD.bat
    echo 2. Select "Run as administrator"
    echo 3. Wait for compilation
    echo.
)

echo [CHECKING] Visual Studio installation:
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo ✓ Visual Studio 2022 found
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo ✓ Visual Studio 2019 found
) else (
    echo ⚠ Visual Studio not found
    echo   Download from: https://visualstudio.microsoft.com/downloads/
)

echo.
echo [CHECKING] Xbox controller:
powershell -Command "if (Get-PnpDevice -Class 'HIDClass' | Where-Object {$_.FriendlyName -like '*Xbox*' -or $_.FriendlyName -like '*Controller*'}) { Write-Host '✓ Xbox controller detected' } else { Write-Host '⚠ Xbox controller not detected' }" 2>nul

echo.
echo [CHECKING] Call of Duty WWII:
if exist "..\s2_mp64_ship.exe" (
    echo ✓ COD WWII found in parent directory
) else (
    echo ⚠ COD WWII executable not found
)

echo.
echo ========================================
echo STATUS CHECK COMPLETE
echo ========================================
echo.
echo This window will stay open so you can read everything.
echo Press any key to close...
pause >nul
