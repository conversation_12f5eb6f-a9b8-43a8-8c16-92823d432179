@echo off

REM This version creates a detailed log file that won't disappear
echo COD WWII Zombies Trainer Build Log > build_results.txt
echo ========================================= >> build_results.txt
echo Build started: %date% %time% >> build_results.txt
echo ========================================= >> build_results.txt
echo. >> build_results.txt

REM Check admin
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Not running as administrator >> build_results.txt
    echo Please right-click and "Run as administrator" >> build_results.txt
    echo. >> build_results.txt
    echo BUILD FAILED - Check build_results.txt for details
    timeout /t 5 >nul
    exit /b 1
) else (
    echo SUCCESS: Running as administrator >> build_results.txt
)

REM Check files
echo Checking source files... >> build_results.txt
if exist "main.cpp" (
    echo ✓ main.cpp found >> build_results.txt
) else (
    echo ✗ main.cpp NOT found >> build_results.txt
)

if exist "Trainer.cpp" (
    echo ✓ Trainer.cpp found >> build_results.txt
) else (
    echo ✗ Trainer.cpp NOT found >> build_results.txt
)

if exist "Memory.cpp" (
    echo ✓ Memory.cpp found >> build_results.txt
) else (
    echo ✗ Memory.cpp NOT found >> build_results.txt
)

if exist "XboxController.cpp" (
    echo ✓ XboxController.cpp found >> build_results.txt
) else (
    echo ✗ XboxController.cpp NOT found >> build_results.txt
)

REM Check Visual Studio
echo. >> build_results.txt
echo Checking Visual Studio installation... >> build_results.txt
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo ✓ Visual Studio 2022 found >> build_results.txt
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >> build_results.txt 2>&1
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    echo ✓ Visual Studio 2019 found >> build_results.txt
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >> build_results.txt 2>&1
) else (
    echo ✗ Visual Studio NOT found >> build_results.txt
    echo Please install Visual Studio 2019 or 2022 with C++ tools >> build_results.txt
    echo Download: https://visualstudio.microsoft.com/downloads/ >> build_results.txt
    echo. >> build_results.txt
    echo BUILD FAILED - Visual Studio required
    timeout /t 5 >nul
    exit /b 1
)

REM Create directory
if not exist "bin" mkdir bin

REM Compile
echo. >> build_results.txt
echo Starting compilation... >> build_results.txt
echo ========================================= >> build_results.txt

cl.exe /EHsc /O2 /MT /I. main.cpp Memory.cpp Trainer.cpp XboxController.cpp /Fe:bin\COD_WWII_Zombies_Trainer.exe /link kernel32.lib user32.lib psapi.lib advapi32.lib xinput.lib >> build_results.txt 2>&1

if %errorLevel% equ 0 (
    echo. >> build_results.txt
    echo ========================================= >> build_results.txt
    echo BUILD SUCCESSFUL! >> build_results.txt
    echo ========================================= >> build_results.txt
    echo Trainer created: bin\COD_WWII_Zombies_Trainer.exe >> build_results.txt
    echo. >> build_results.txt
    echo NEXT STEPS: >> build_results.txt
    echo 1. Connect Xbox controller >> build_results.txt
    echo 2. Launch COD WWII Zombies >> build_results.txt
    echo 3. Run bin\COD_WWII_Zombies_Trainer.exe as Administrator >> build_results.txt
    echo. >> build_results.txt
    
    echo BUILD SUCCESSFUL! Check build_results.txt for details.
    echo Trainer created: bin\COD_WWII_Zombies_Trainer.exe
    timeout /t 10 >nul
) else (
    echo. >> build_results.txt
    echo ========================================= >> build_results.txt
    echo BUILD FAILED! >> build_results.txt
    echo ========================================= >> build_results.txt
    echo Check error messages above for details. >> build_results.txt
    
    echo BUILD FAILED! Check build_results.txt for error details.
    timeout /t 10 >nul
)
