@echo off
title Install Visual Studio for COD Trainer
color 0B

echo.
echo ========================================
echo Install Visual Studio for COD Trainer
echo ========================================
echo.

echo This will help you install Visual Studio Community 2022 (FREE)
echo which is needed to build the COD WWII trainer.
echo.

echo STEP 1: Download Visual Studio
echo ✓ Opening download page in your browser...
echo.

REM Open Visual Studio download page
start https://visualstudio.microsoft.com/downloads/

echo STEP 2: Installation Instructions
echo.
echo When the download page opens:
echo 1. Click "Free download" under "Community 2022"
echo 2. Run the downloaded installer
echo 3. In the installer, make sure to check:
echo    ☑ "Desktop development with C++"
echo 4. Click Install and wait for completion
echo.

echo STEP 3: After Installation
echo 1. Come back to this folder
echo 2. Right-click BUILD_SIMPLE.bat
echo 3. Select "Run as administrator"
echo 4. Your trainer will build successfully!
echo.

echo ========================================
echo ALTERNATIVE: Try MinGW (No VS needed)
echo ========================================
echo.
echo If you don't want to install Visual Studio:
echo 1. Right-click BUILD_MINGW.bat
echo 2. Select "Run as administrator"
echo 3. Follow the MinGW installation instructions
echo.

echo ========================================
echo QUICK OPTION: Download Pre-built
echo ========================================
echo.
echo For a quick test (without Xbox controller):
echo 1. Right-click DOWNLOAD_TRAINER.bat
echo 2. Select "Run as administrator"
echo 3. This downloads a basic trainer
echo.

echo Press any key to close this window...
pause >nul
