#include <Windows.h>
#include <iostream>
#include <TlHelp32.h>
#include <string>
#include <vector>

void ListAllProcesses() {
    std::cout << "\n=== ALL RUNNING PROCESSES ===" << std::endl;
    
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) {
        std::cout << "Error: Could not create process snapshot!" << std::endl;
        return;
    }
    
    PROCESSENTRY32W processEntry;
    processEntry.dwSize = sizeof(PROCESSENTRY32W);
    
    std::vector<std::wstring> codProcesses;
    
    if (Process32FirstW(snapshot, &processEntry)) {
        do {
            std::wstring processName = processEntry.szExeFile;
            
            // Look for any COD-related processes
            if (processName.find(L"cod") != std::wstring::npos ||
                processName.find(L"COD") != std::wstring::npos ||
                processName.find(L"s2_") != std::wstring::npos ||
                processName.find(L"ww2") != std::wstring::npos ||
                processName.find(L"WW2") != std::wstring::npos) {
                codProcesses.push_back(processName);
                std::wcout << L"FOUND COD PROCESS: " << processName << L" (PID: " << processEntry.th32ProcessID << L")" << std::endl;
            }
        } while (Process32NextW(snapshot, &processEntry));
    }
    
    CloseHandle(snapshot);
    
    if (codProcesses.empty()) {
        std::cout << "\nNO COD PROCESSES FOUND!" << std::endl;
        std::cout << "Make sure Call of Duty WWII is running and you're in Zombies mode." << std::endl;
    } else {
        std::cout << "\nFound " << codProcesses.size() << " COD-related processes." << std::endl;
    }
}

void CheckSpecificProcesses() {
    std::cout << "\n=== CHECKING SPECIFIC PROCESSES ===" << std::endl;
    
    std::vector<std::wstring> processesToCheck = {
        L"s2_mp64_ship.exe",
        L"s2_sp64_ship.exe", 
        L"codwwii.exe",
        L"CODWWII.exe",
        L"CallofDutyWWII.exe"
    };
    
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) return;
    
    PROCESSENTRY32W processEntry;
    processEntry.dwSize = sizeof(PROCESSENTRY32W);
    
    for (const auto& targetProcess : processesToCheck) {
        bool found = false;
        
        if (Process32FirstW(snapshot, &processEntry)) {
            do {
                if (targetProcess == processEntry.szExeFile) {
                    std::wcout << L"✓ FOUND: " << targetProcess << L" (PID: " << processEntry.th32ProcessID << L")" << std::endl;
                    found = true;
                    break;
                }
            } while (Process32NextW(snapshot, &processEntry));
        }
        
        if (!found) {
            std::wcout << L"✗ NOT FOUND: " << targetProcess << std::endl;
        }
        
        // Reset for next search
        if (Process32FirstW(snapshot, &processEntry)) {
            // Reset position for next search
        }
    }
    
    CloseHandle(snapshot);
}

int main() {
    std::cout << "COD WWII Process Checker" << std::endl;
    std::cout << "========================" << std::endl;
    
    std::cout << "\nThis tool will help diagnose why the trainer can't find COD WWII." << std::endl;
    
    ListAllProcesses();
    CheckSpecificProcesses();
    
    std::cout << "\n=== INSTRUCTIONS ===" << std::endl;
    std::cout << "1. Make sure Call of Duty WWII is running" << std::endl;
    std::cout << "2. Make sure you're IN a Zombies match (not just menu)" << std::endl;
    std::cout << "3. Try both Steam and non-Steam versions" << std::endl;
    std::cout << "4. Run this checker again after launching the game" << std::endl;
    
    std::cout << "\nPress any key to exit..." << std::endl;
    system("pause");
    return 0;
}
