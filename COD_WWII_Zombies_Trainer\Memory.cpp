#include "Memory.h"
#include <iostream>

Memory::Memory() : processHandle(nullptr), processId(0), moduleBase(0) {
    SetDebugPrivileges();
}

Memory::~Memory() {
    DetachFromProcess();
}

bool Memory::AttachToProcess(const std::wstring& processName) {
    processId = GetProcessId(processName);
    if (processId == 0) {
        return false;
    }
    
    processHandle = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
    if (processHandle == nullptr) {
        return false;
    }
    
    moduleBase = GetModuleBaseAddress(processName, processId);
    if (moduleBase == 0) {
        CloseHandle(processHandle);
        processHandle = nullptr;
        return false;
    }
    
    return true;
}

void Memory::DetachFromProcess() {
    if (processHandle) {
        CloseHandle(processHandle);
        processHandle = nullptr;
    }
    processId = 0;
    moduleBase = 0;
}

bool Memory::IsProcessValid() {
    if (!processHandle) return false;
    
    DWORD exitCode;
    if (GetExitCodeProcess(processHandle, &exitCode)) {
        return exitCode == STILL_ACTIVE;
    }
    return false;
}

bool Memory::ReadBytes(uintptr_t address, void* buffer, size_t size) {
    return ReadProcessMemory(processHandle, reinterpret_cast<LPCVOID>(address), buffer, size, nullptr);
}

bool Memory::WriteBytes(uintptr_t address, const void* buffer, size_t size) {
    return WriteProcessMemory(processHandle, reinterpret_cast<LPVOID>(address), buffer, size, nullptr);
}

uintptr_t Memory::ResolvePointer(uintptr_t baseAddress, const std::vector<uintptr_t>& offsets) {
    uintptr_t address = Read<uintptr_t>(baseAddress);
    
    for (size_t i = 0; i < offsets.size() - 1; ++i) {
        address = Read<uintptr_t>(address + offsets[i]);
        if (address == 0) return 0;
    }
    
    return address + offsets.back();
}

DWORD Memory::GetProcessId(const std::wstring& processName) {
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    PROCESSENTRY32W processEntry;
    processEntry.dwSize = sizeof(PROCESSENTRY32W);
    
    if (Process32FirstW(snapshot, &processEntry)) {
        do {
            if (processName == processEntry.szExeFile) {
                CloseHandle(snapshot);
                return processEntry.th32ProcessID;
            }
        } while (Process32NextW(snapshot, &processEntry));
    }
    
    CloseHandle(snapshot);
    return 0;
}

uintptr_t Memory::GetModuleBaseAddress(const std::wstring& moduleName, DWORD processId) {
    HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, processId);
    if (snapshot == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    MODULEENTRY32W moduleEntry;
    moduleEntry.dwSize = sizeof(MODULEENTRY32W);
    
    if (Module32FirstW(snapshot, &moduleEntry)) {
        do {
            if (moduleName == moduleEntry.szModule) {
                CloseHandle(snapshot);
                return reinterpret_cast<uintptr_t>(moduleEntry.modBaseAddr);
            }
        } while (Module32NextW(snapshot, &moduleEntry));
    }
    
    CloseHandle(snapshot);
    return 0;
}

uintptr_t Memory::PatternScan(const std::string& pattern, const std::string& mask) {
    MEMORY_BASIC_INFORMATION mbi;
    uintptr_t address = 0;
    
    while (VirtualQueryEx(processHandle, reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi))) {
        if (mbi.State == MEM_COMMIT && (mbi.Protect & PAGE_GUARD) == 0) {
            std::vector<char> buffer(mbi.RegionSize);
            SIZE_T bytesRead;
            
            if (ReadProcessMemory(processHandle, mbi.BaseAddress, buffer.data(), mbi.RegionSize, &bytesRead)) {
                for (size_t i = 0; i < bytesRead - pattern.length(); ++i) {
                    bool found = true;
                    for (size_t j = 0; j < pattern.length(); ++j) {
                        if (mask[j] == 'x' && buffer[i + j] != pattern[j]) {
                            found = false;
                            break;
                        }
                    }
                    if (found) {
                        return reinterpret_cast<uintptr_t>(mbi.BaseAddress) + i;
                    }
                }
            }
        }
        address += mbi.RegionSize;
    }
    
    return 0;
}

bool Memory::BypassCheatEngineDetection() {
    // Based on the thread info: CE blocker address for MP is 0x226670
    // For zombies, we need to find the equivalent address
    uintptr_t ceBlockerAddress = moduleBase + 0x226670; // This might need adjustment for zombies
    
    // Try to patch the CE detection
    BYTE nopBytes[] = { 0x90, 0x90, 0x90, 0x90, 0x90, 0x90 }; // NOP instructions
    
    if (WriteBytes(ceBlockerAddress, nopBytes, sizeof(nopBytes))) {
        std::cout << "Cheat Engine detection bypassed successfully!" << std::endl;
        return true;
    }
    
    std::cout << "Warning: Could not bypass Cheat Engine detection." << std::endl;
    return false;
}

bool Memory::SetDebugPrivileges() {
    HANDLE token;
    TOKEN_PRIVILEGES privileges;
    
    if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &token)) {
        return false;
    }
    
    privileges.PrivilegeCount = 1;
    privileges.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;
    
    if (!LookupPrivilegeValue(nullptr, SE_DEBUG_NAME, &privileges.Privileges[0].Luid)) {
        CloseHandle(token);
        return false;
    }
    
    bool result = AdjustTokenPrivileges(token, FALSE, &privileges, 0, nullptr, nullptr);
    CloseHandle(token);
    
    return result;
}
