#pragma once
#include <Windows.h>
#include <Xinput.h>
#include <cmath>

#pragma comment(lib, "xinput.lib")

class XboxController {
private:
    DWORD controllerIndex;
    XINPUT_STATE currentState;
    XINPUT_STATE previousState;
    bool connected;
    
    // Deadzone settings
    static constexpr float LEFT_THUMB_DEADZONE = 7849.0f / 32767.0f;
    static constexpr float RIGHT_THUMB_DEADZONE = 8689.0f / 32767.0f;
    static constexpr float TRIGGER_THRESHOLD = 30.0f / 255.0f;
    
public:
    XboxController(DWORD index = 0);
    ~XboxController();
    
    // Update controller state
    bool Update();
    bool IsConnected() const { return connected; }
    
    // Button states
    bool IsButtonPressed(WORD button) const;
    bool IsButtonJustPressed(WORD button) const;
    bool IsButtonJustReleased(WORD button) const;
    
    // Analog stick values (normalized -1.0 to 1.0)
    float GetLeftStickX() const;
    float GetLeftStickY() const;
    float GetRightStickX() const;
    float GetRightStickY() const;
    
    // Trigger values (normalized 0.0 to 1.0)
    float GetLeftTrigger() const;
    float GetRightTrigger() const;
    
    // Vibration
    void SetVibration(float leftMotor, float rightMotor);
    void StopVibration();
    
    // Helper functions
    bool IsAiming() const; // Left trigger pressed
    bool IsFiring() const; // Right trigger pressed
    bool IsMoving() const; // Left stick moved
    bool IsLooking() const; // Right stick moved
    
private:
    float ApplyDeadzone(float value, float deadzone) const;
    float NormalizeStick(SHORT value, float deadzone) const;
    float NormalizeTrigger(BYTE value) const;
};

// Xbox controller button constants for easy reference
namespace XboxButtons {
    constexpr WORD DPAD_UP = XINPUT_GAMEPAD_DPAD_UP;
    constexpr WORD DPAD_DOWN = XINPUT_GAMEPAD_DPAD_DOWN;
    constexpr WORD DPAD_LEFT = XINPUT_GAMEPAD_DPAD_LEFT;
    constexpr WORD DPAD_RIGHT = XINPUT_GAMEPAD_DPAD_RIGHT;
    constexpr WORD START = XINPUT_GAMEPAD_START;
    constexpr WORD BACK = XINPUT_GAMEPAD_BACK;
    constexpr WORD LEFT_THUMB = XINPUT_GAMEPAD_LEFT_THUMB;
    constexpr WORD RIGHT_THUMB = XINPUT_GAMEPAD_RIGHT_THUMB;
    constexpr WORD LEFT_SHOULDER = XINPUT_GAMEPAD_LEFT_SHOULDER;
    constexpr WORD RIGHT_SHOULDER = XINPUT_GAMEPAD_RIGHT_SHOULDER;
    constexpr WORD A = XINPUT_GAMEPAD_A;
    constexpr WORD B = XINPUT_GAMEPAD_B;
    constexpr WORD X = XINPUT_GAMEPAD_X;
    constexpr WORD Y = XINPUT_GAMEPAD_Y;
}
