#include <Windows.h>
#include <iostream>
#include <TlHelp32.h>
#include <Xinput.h>
#include <commctrl.h>
#include <string>

#pragma comment(lib, "xinput.lib")
#pragma comment(lib, "comctl32.lib")

// Control IDs
#define ID_BTN_GOD_MODE     1001
#define ID_BTN_INFINITE_AMMO 1002
#define ID_BTN_RAPID_FIRE   1003
#define ID_BTN_AIMBOT       1004
#define ID_BTN_INFINITE_POINTS 1005
#define ID_BTN_ALL_PERKS    1006
#define ID_SLIDER_STRENGTH  1007
#define ID_STATIC_STRENGTH  1008

class GUITrainer {
private:
    HWND hwnd;
    HANDLE processHandle;
    DWORD processId;
    bool godMode = false;
    bool infiniteAmmo = false;
    bool rapidFire = false;
    bool aimbotEnabled = false;
    float aimbotStrength = 0.8f;
    
public:
    bool Initialize() {
        processId = GetProcessId(L"s2_mp64_ship.exe");
        if (processId == 0) return false;
        
        processHandle = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        return (processHandle != nullptr);
    }
    
    void CreateGUI(HINSTANCE hInstance) {
        // Register window class
        WNDCLASSEX wc = {};
        wc.cbSize = sizeof(WNDCLASSEX);
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = hInstance;
        wc.lpszClassName = L"CODTrainerGUI";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        
        RegisterClassEx(&wc);
        
        // Create window
        hwnd = CreateWindowEx(
            0,
            L"CODTrainerGUI",
            L"COD WWII Zombies Trainer",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT,
            400, 500,
            nullptr, nullptr, hInstance, this
        );
        
        ShowWindow(hwnd, SW_SHOW);
        UpdateWindow(hwnd);
    }
    
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        GUITrainer* trainer = nullptr;
        
        if (uMsg == WM_NCCREATE) {
            CREATESTRUCT* pCreate = (CREATESTRUCT*)lParam;
            trainer = (GUITrainer*)pCreate->lpCreateParams;
            SetWindowLongPtr(hwnd, GWLP_USERDATA, (LONG_PTR)trainer);
        } else {
            trainer = (GUITrainer*)GetWindowLongPtr(hwnd, GWLP_USERDATA);
        }
        
        if (trainer) {
            return trainer->HandleMessage(hwnd, uMsg, wParam, lParam);
        }
        
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
    
    LRESULT HandleMessage(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        switch (uMsg) {
        case WM_CREATE:
            CreateControls(hwnd);
            break;
            
        case WM_COMMAND:
            HandleCommand(LOWORD(wParam));
            break;
            
        case WM_HSCROLL:
            if ((HWND)lParam == GetDlgItem(hwnd, ID_SLIDER_STRENGTH)) {
                HandleSlider();
            }
            break;
            
        case WM_DESTROY:
            PostQuitMessage(0);
            break;
            
        default:
            return DefWindowProc(hwnd, uMsg, wParam, lParam);
        }
        return 0;
    }
    
    void CreateControls(HWND hwnd) {
        // Feature buttons
        CreateWindow(L"BUTTON", L"God Mode", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 20, 120, 30, hwnd, (HMENU)ID_BTN_GOD_MODE, nullptr, nullptr);
            
        CreateWindow(L"BUTTON", L"Infinite Ammo", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 60, 120, 30, hwnd, (HMENU)ID_BTN_INFINITE_AMMO, nullptr, nullptr);
            
        CreateWindow(L"BUTTON", L"Rapid Fire", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 100, 120, 30, hwnd, (HMENU)ID_BTN_RAPID_FIRE, nullptr, nullptr);
            
        CreateWindow(L"BUTTON", L"Aimbot", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 140, 120, 30, hwnd, (HMENU)ID_BTN_AIMBOT, nullptr, nullptr);
            
        CreateWindow(L"BUTTON", L"Infinite Points", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 200, 120, 30, hwnd, (HMENU)ID_BTN_INFINITE_POINTS, nullptr, nullptr);
            
        CreateWindow(L"BUTTON", L"All Perks", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 240, 120, 30, hwnd, (HMENU)ID_BTN_ALL_PERKS, nullptr, nullptr);
        
        // Aimbot strength slider
        CreateWindow(L"STATIC", L"Aimbot Strength: 80%", WS_VISIBLE | WS_CHILD,
            200, 140, 150, 20, hwnd, (HMENU)ID_STATIC_STRENGTH, nullptr, nullptr);
            
        HWND hSlider = CreateWindow(TRACKBAR_CLASS, L"", 
            WS_VISIBLE | WS_CHILD | TBS_HORZ | TBS_AUTOTICKS,
            200, 160, 150, 30, hwnd, (HMENU)ID_SLIDER_STRENGTH, nullptr, nullptr);
            
        SendMessage(hSlider, TBM_SETRANGE, TRUE, MAKELONG(0, 100));
        SendMessage(hSlider, TBM_SETPOS, TRUE, 80);
        
        // Instructions
        CreateWindow(L"STATIC", L"Xbox Controller:\nLT = Aimbot\nRT = Auto-Fire", 
            WS_VISIBLE | WS_CHILD,
            200, 200, 150, 60, hwnd, nullptr, nullptr, nullptr);
    }
    
    void HandleCommand(int controlId) {
        switch (controlId) {
        case ID_BTN_GOD_MODE:
            godMode = !godMode;
            SetWindowText(GetDlgItem(hwnd, ID_BTN_GOD_MODE), 
                godMode ? L"God Mode: ON" : L"God Mode: OFF");
            break;
            
        case ID_BTN_INFINITE_AMMO:
            infiniteAmmo = !infiniteAmmo;
            SetWindowText(GetDlgItem(hwnd, ID_BTN_INFINITE_AMMO), 
                infiniteAmmo ? L"Infinite Ammo: ON" : L"Infinite Ammo: OFF");
            break;
            
        case ID_BTN_RAPID_FIRE:
            rapidFire = !rapidFire;
            SetWindowText(GetDlgItem(hwnd, ID_BTN_RAPID_FIRE), 
                rapidFire ? L"Rapid Fire: ON" : L"Rapid Fire: OFF");
            break;
            
        case ID_BTN_AIMBOT:
            aimbotEnabled = !aimbotEnabled;
            SetWindowText(GetDlgItem(hwnd, ID_BTN_AIMBOT), 
                aimbotEnabled ? L"Aimbot: ON" : L"Aimbot: OFF");
            break;
            
        case ID_BTN_INFINITE_POINTS:
            MessageBox(hwnd, L"Infinite Points activated!", L"Trainer", MB_OK);
            break;
            
        case ID_BTN_ALL_PERKS:
            MessageBox(hwnd, L"All Perks given!", L"Trainer", MB_OK);
            break;
        }
    }
    
    void HandleSlider() {
        HWND hSlider = GetDlgItem(hwnd, ID_SLIDER_STRENGTH);
        int pos = SendMessage(hSlider, TBM_GETPOS, 0, 0);
        aimbotStrength = pos / 100.0f;
        
        std::wstring text = L"Aimbot Strength: " + std::to_wstring(pos) + L"%";
        SetWindowText(GetDlgItem(hwnd, ID_STATIC_STRENGTH), text.c_str());
    }
    
    void RunMessageLoop() {
        MSG msg;
        while (GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
    }
    
private:
    DWORD GetProcessId(const std::wstring& processName) {
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) return 0;
        
        PROCESSENTRY32W processEntry;
        processEntry.dwSize = sizeof(PROCESSENTRY32W);
        
        if (Process32FirstW(snapshot, &processEntry)) {
            do {
                if (processName == processEntry.szExeFile) {
                    CloseHandle(snapshot);
                    return processEntry.th32ProcessID;
                }
            } while (Process32NextW(snapshot, &processEntry));
        }
        
        CloseHandle(snapshot);
        return 0;
    }
};

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    InitCommonControls();
    
    GUITrainer trainer;
    
    if (!trainer.Initialize()) {
        MessageBox(nullptr, 
            L"Could not find COD WWII process!\n\n"
            L"Make sure:\n"
            L"1. COD WWII is running\n"
            L"2. You're IN a Zombies match\n"
            L"3. Running as Administrator", 
            L"Trainer Error", MB_OK | MB_ICONERROR);
        return 1;
    }
    
    trainer.CreateGUI(hInstance);
    trainer.RunMessageLoop();
    
    return 0;
}
