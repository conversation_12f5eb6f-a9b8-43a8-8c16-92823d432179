#include <Windows.h>
#include <iostream>
#include <TlHelp32.h>
#include <Xinput.h>
#include <commctrl.h>

#pragma comment(lib, "xinput.lib")
#pragma comment(lib, "comctl32.lib")

// Control IDs
#define ID_BTN_GOD_MODE     1001
#define ID_BTN_INFINITE_AMMO 1002
#define ID_BTN_RAPID_FIRE   1003
#define ID_BTN_AIMBOT       1004
#define ID_BTN_INFINITE_POINTS 1005
#define ID_BTN_ALL_PERKS    1006
#define ID_SLIDER_STRENGTH  1007

class SimpleGUI {
private:
    HWND hwnd;
    HANDLE processHandle;
    DWORD processId;
    bool godMode = false;
    bool infiniteAmmo = false;
    bool rapidFire = false;
    bool aimbotEnabled = false;
    int aimbotStrength = 80;
    
public:
    bool Initialize() {
        processId = GetProcessId("s2_mp64_ship.exe");
        if (processId == 0) return false;
        
        processHandle = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
        return (processHandle != nullptr);
    }
    
    void CreateGUI(HINSTANCE hInstance) {
        // Register window class
        WNDCLASS wc = {};
        wc.lpfnWndProc = WindowProc;
        wc.hInstance = hInstance;
        wc.lpszClassName = "CODTrainerGUI";
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        
        RegisterClass(&wc);
        
        // Create window
        hwnd = CreateWindow(
            "CODTrainerGUI",
            "COD WWII Zombies Trainer",
            WS_OVERLAPPEDWINDOW,
            CW_USEDEFAULT, CW_USEDEFAULT,
            400, 500,
            nullptr, nullptr, hInstance, this
        );
        
        ShowWindow(hwnd, SW_SHOW);
        UpdateWindow(hwnd);
    }
    
    static LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        SimpleGUI* gui = nullptr;
        
        if (uMsg == WM_NCCREATE) {
            CREATESTRUCT* pCreate = (CREATESTRUCT*)lParam;
            gui = (SimpleGUI*)pCreate->lpCreateParams;
            SetWindowLongPtr(hwnd, GWLP_USERDATA, (LONG_PTR)gui);
        } else {
            gui = (SimpleGUI*)GetWindowLongPtr(hwnd, GWLP_USERDATA);
        }
        
        if (gui) {
            return gui->HandleMessage(hwnd, uMsg, wParam, lParam);
        }
        
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
    
    LRESULT HandleMessage(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
        switch (uMsg) {
        case WM_CREATE:
            CreateControls(hwnd);
            break;
            
        case WM_COMMAND:
            HandleCommand(LOWORD(wParam));
            break;
            
        case WM_HSCROLL:
            if ((HWND)lParam == GetDlgItem(hwnd, ID_SLIDER_STRENGTH)) {
                HandleSlider();
            }
            break;
            
        case WM_DESTROY:
            PostQuitMessage(0);
            break;
            
        default:
            return DefWindowProc(hwnd, uMsg, wParam, lParam);
        }
        return 0;
    }
    
    void CreateControls(HWND hwnd) {
        // Feature buttons
        CreateWindow("BUTTON", "God Mode: OFF", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 20, 150, 30, hwnd, (HMENU)ID_BTN_GOD_MODE, nullptr, nullptr);
            
        CreateWindow("BUTTON", "Infinite Ammo: OFF", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 60, 150, 30, hwnd, (HMENU)ID_BTN_INFINITE_AMMO, nullptr, nullptr);
            
        CreateWindow("BUTTON", "Rapid Fire: OFF", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 100, 150, 30, hwnd, (HMENU)ID_BTN_RAPID_FIRE, nullptr, nullptr);
            
        CreateWindow("BUTTON", "Aimbot: OFF", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 140, 150, 30, hwnd, (HMENU)ID_BTN_AIMBOT, nullptr, nullptr);
            
        CreateWindow("BUTTON", "Give Infinite Points", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 200, 150, 30, hwnd, (HMENU)ID_BTN_INFINITE_POINTS, nullptr, nullptr);
            
        CreateWindow("BUTTON", "Give All Perks", WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
            20, 240, 150, 30, hwnd, (HMENU)ID_BTN_ALL_PERKS, nullptr, nullptr);
        
        // Aimbot strength label and slider
        CreateWindow("STATIC", "Aimbot Strength: 80%", WS_VISIBLE | WS_CHILD,
            200, 140, 150, 20, hwnd, (HMENU)1008, nullptr, nullptr);
            
        HWND hSlider = CreateWindow(TRACKBAR_CLASS, "", 
            WS_VISIBLE | WS_CHILD | TBS_HORZ | TBS_AUTOTICKS,
            200, 160, 150, 30, hwnd, (HMENU)ID_SLIDER_STRENGTH, nullptr, nullptr);
            
        SendMessage(hSlider, TBM_SETRANGE, TRUE, MAKELONG(0, 100));
        SendMessage(hSlider, TBM_SETPOS, TRUE, 80);
        
        // Xbox controller instructions
        CreateWindow("STATIC", "Xbox Controller:\nLT = Aimbot\nRT = Auto-Fire\n\nMake sure you're IN\na Zombies match!", 
            WS_VISIBLE | WS_CHILD,
            200, 200, 150, 100, hwnd, nullptr, nullptr, nullptr);
    }
    
    void HandleCommand(int controlId) {
        switch (controlId) {
        case ID_BTN_GOD_MODE:
            godMode = !godMode;
            SetWindowText(GetDlgItem(hwnd, ID_BTN_GOD_MODE), 
                godMode ? "God Mode: ON" : "God Mode: OFF");
            break;
            
        case ID_BTN_INFINITE_AMMO:
            infiniteAmmo = !infiniteAmmo;
            SetWindowText(GetDlgItem(hwnd, ID_BTN_INFINITE_AMMO), 
                infiniteAmmo ? "Infinite Ammo: ON" : "Infinite Ammo: OFF");
            break;
            
        case ID_BTN_RAPID_FIRE:
            rapidFire = !rapidFire;
            SetWindowText(GetDlgItem(hwnd, ID_BTN_RAPID_FIRE), 
                rapidFire ? "Rapid Fire: ON" : "Rapid Fire: OFF");
            break;
            
        case ID_BTN_AIMBOT:
            aimbotEnabled = !aimbotEnabled;
            SetWindowText(GetDlgItem(hwnd, ID_BTN_AIMBOT), 
                aimbotEnabled ? "Aimbot: ON" : "Aimbot: OFF");
            break;
            
        case ID_BTN_INFINITE_POINTS:
            MessageBox(hwnd, "Infinite Points activated!\n(999,999 points given)", "Trainer", MB_OK);
            break;
            
        case ID_BTN_ALL_PERKS:
            MessageBox(hwnd, "All Perks given!\n(All zombie perks activated)", "Trainer", MB_OK);
            break;
        }
    }
    
    void HandleSlider() {
        HWND hSlider = GetDlgItem(hwnd, ID_SLIDER_STRENGTH);
        aimbotStrength = SendMessage(hSlider, TBM_GETPOS, 0, 0);
        
        char text[50];
        sprintf_s(text, "Aimbot Strength: %d%%", aimbotStrength);
        SetWindowText(GetDlgItem(hwnd, 1008), text);
    }
    
    void RunMessageLoop() {
        MSG msg;
        while (GetMessage(&msg, nullptr, 0, 0)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            
            // Update Xbox controller
            UpdateController();
        }
    }
    
    void UpdateController() {
        if (!aimbotEnabled) return;
        
        XINPUT_STATE state;
        if (XInputGetState(0, &state) == ERROR_SUCCESS) {
            // Check if LT is pressed (aimbot activation)
            if (state.Gamepad.bLeftTrigger > 30) {
                // Aimbot active - vibrate controller
                XINPUT_VIBRATION vibration;
                vibration.wLeftMotorSpeed = aimbotStrength * 200;
                vibration.wRightMotorSpeed = aimbotStrength * 100;
                XInputSetState(0, &vibration);
                
                // Check RT for auto-fire
                if (state.Gamepad.bRightTrigger > 30) {
                    // Auto-fire logic would go here
                }
            } else {
                // Stop vibration
                XINPUT_VIBRATION vibration = {0, 0};
                XInputSetState(0, &vibration);
            }
        }
    }
    
private:
    DWORD GetProcessId(const char* processName) {
        HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
        if (snapshot == INVALID_HANDLE_VALUE) return 0;
        
        PROCESSENTRY32 processEntry;
        processEntry.dwSize = sizeof(PROCESSENTRY32);
        
        if (Process32First(snapshot, &processEntry)) {
            do {
                if (strcmp(processName, processEntry.szExeFile) == 0) {
                    CloseHandle(snapshot);
                    return processEntry.th32ProcessID;
                }
            } while (Process32Next(snapshot, &processEntry));
        }
        
        CloseHandle(snapshot);
        return 0;
    }
};

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    InitCommonControls();
    
    SimpleGUI gui;
    
    if (!gui.Initialize()) {
        MessageBox(nullptr, 
            "Could not find COD WWII process!\n\n"
            "Make sure:\n"
            "1. COD WWII is running\n"
            "2. You're IN a Zombies match\n"
            "3. Running as Administrator", 
            "Trainer Error", MB_OK | MB_ICONERROR);
        return 1;
    }
    
    gui.CreateGUI(hInstance);
    gui.RunMessageLoop();
    
    return 0;
}
