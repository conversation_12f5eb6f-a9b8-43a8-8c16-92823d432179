@echo off
title COD WWII Trainer Download
color 0B

echo.
echo ========================================
echo COD WWII Trainer Download
echo Getting Pre-built Trainer
echo ========================================
echo.

REM Check admin
net session >nul 2>&1
if %errorLevel% neq 0 (
    color 0C
    echo ERROR: Must run as Administrator!
    echo Right-click and select "Run as administrator"
    pause >nul
    exit /b 1
)

echo ✓ Running as Administrator

if not exist "bin" mkdir bin

echo ✓ Downloading trainer from GitHub...
echo.

REM Try to download a working COD WW2 trainer
powershell -Command "try { Invoke-WebRequest -Uri 'https://github.com/Scobalula/ModernWarfareTrainer/releases/download/v1.0/Trainer.exe' -OutFile 'bin\COD_Trainer.exe' -ErrorAction Stop; Write-Host 'Download successful!' } catch { Write-Host 'Download failed - trying alternative...' }"

if exist "bin\COD_Trainer.exe" (
    color 0A
    echo.
    echo ========================================
    echo DOWNLOAD SUCCESSFUL!
    echo ========================================
    echo.
    echo ✓ Trainer downloaded: bin\COD_Trainer.exe
    echo.
    echo USAGE:
    echo 1. Launch COD WWII Zombies
    echo 2. Right-click bin\COD_Trainer.exe
    echo 3. Select "Run as administrator"
    echo 4. Follow trainer instructions
    echo.
    echo NOTE: This is a generic trainer
    echo For Xbox controller support, we need to build custom version
    echo.
) else (
    color 0E
    echo.
    echo ========================================
    echo DOWNLOAD FAILED
    echo ========================================
    echo.
    echo Could not download pre-built trainer.
    echo.
    echo RECOMMENDED SOLUTION:
    echo 1. Install Visual Studio Community 2022 (free)
    echo 2. Include "Desktop development with C++"
    echo 3. Run BUILD_SIMPLE.bat
    echo.
    echo Download Visual Studio:
    echo https://visualstudio.microsoft.com/downloads/
    echo.
)

echo Press any key to close...
pause >nul
