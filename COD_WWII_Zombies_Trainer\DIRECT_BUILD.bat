@echo off
title COD WWII Zombies Trainer - Direct Build
echo.
echo ========================================
echo COD WWII Zombies Trainer - Direct Build
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    color 0C
    echo ERROR: Must run as Administrator!
    echo Right-click on DIRECT_BUILD.bat and select "Run as administrator"
    pause
    exit /b 1
)

REM Try to find Visual Studio compiler
set "VS2022=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
set "VS2019=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"

if exist "%VS2022%" (
    call "%VS2022%" >nul 2>&1
    echo Found Visual Studio 2022
) else if exist "%VS2019%" (
    call "%VS2019%" >nul 2>&1
    echo Found Visual Studio 2019
) else (
    color 0C
    echo ERROR: Visual Studio not found!
    echo Please install Visual Studio 2019 or 2022 with C++ development tools
    echo Download from: https://visualstudio.microsoft.com/downloads/
    pause
    exit /b 1
)

REM Create output directory
if not exist "bin" mkdir bin

echo.
echo Compiling trainer...
echo.

REM Compile directly with cl.exe
cl.exe /EHsc /O2 /MT ^
    /I. ^
    main.cpp Memory.cpp Trainer.cpp XboxController.cpp ^
    /Fe:bin\COD_WWII_Zombies_Trainer.exe ^
    /link kernel32.lib user32.lib psapi.lib advapi32.lib xinput.lib

if %errorLevel% equ 0 (
    color 0A
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo Trainer location: bin\COD_WWII_Zombies_Trainer.exe
    echo.
    echo READY TO USE:
    echo 1. Connect Xbox controller to PC
    echo 2. Launch Call of Duty WWII Zombies
    echo 3. Run bin\COD_WWII_Zombies_Trainer.exe as Administrator
    echo.
    echo CONTROLLER CONTROLS:
    echo - LT (Left Trigger): Activate Aimbot
    echo - RT (Right Trigger): Auto-Fire
    echo.
    echo All other features controlled via GUI!
    echo.
) else (
    color 0C
    echo.
    echo BUILD FAILED!
    echo Make sure Visual Studio C++ tools are installed.
    echo.
)

echo.
echo ========================================
echo WINDOW WILL STAY OPEN - READ ABOVE
echo ========================================
echo.
echo Press any key to close this window...
pause >nul
