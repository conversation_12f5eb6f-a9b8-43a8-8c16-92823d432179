#include "resource.h"
#include <windows.h>

IDD_MAIN_DIALOG DIALOGEX 0, 0, 400, 500
STYLE DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU | WS_MINIMIZEBOX
CAPTION "COD WWII Zombies Ultimate Trainer v1.0"
FONT 8, "MS Shell Dlg"
BEGIN
    // Title and connection
    LTEXT           "COD WWII Zombies Ultimate Trainer", IDC_STATIC, 10, 10, 200, 12, SS_LEFT
    PUSHBUTTON      "Attach to Game", IDC_BTN_ATTACH, 10, 25, 100, 25
    LTEXT           "Status: Ready", IDC_STATIC_STATUS, 120, 30, 270, 12, SS_LEFT
    
    // Combat Features Group
    GROUPBOX        "Combat Features", IDC_STATIC, 10, 60, 180, 140
    AUTOCHECKBOX    "God Mode", IDC_CHK_GODMODE, 20, 80, 80, 12
    AUTOCHECKBOX    "Infinite Ammo", IDC_CHK_INFINITE_AMMO, 20, 100, 80, 12
    AUTOCHECKBOX    "Rapid Fire", IDC_CHK_RAPID_FIRE, 20, 120, 80, 12
    AUTOCHECKBOX    "No Recoil", IDC_CHK_NO_RECOIL, 20, 140, 80, 12
    AUTOCHECKBOX    "Aimbot", IDC_CHK_AIMBOT, 20, 160, 80, 12
    AUTOCHECKBOX    "No Fall Damage", IDC_CHK_NO_FALL_DAMAGE, 20, 180, 80, 12
    
    // Movement Features Group
    GROUPBOX        "Movement Features", IDC_STATIC, 200, 60, 180, 60
    AUTOCHECKBOX    "Super Jump", IDC_CHK_SUPER_JUMP, 210, 80, 80, 12

    // Aimbot Settings Group
    GROUPBOX        "Aimbot Settings (Controller: LT+RT)", IDC_STATIC, 200, 130, 180, 120
    LTEXT           "FOV: 90°", IDC_STATIC_FOV, 210, 150, 60, 12, SS_LEFT
    CONTROL         "", IDC_SLIDER_AIMBOT_FOV, "msctls_trackbar32", TBS_HORZ | TBS_AUTOTICKS | WS_TABSTOP, 210, 165, 160, 20
    LTEXT           "Smoothness: 2.0", IDC_STATIC_SMOOTH, 210, 185, 80, 12, SS_LEFT
    CONTROL         "", IDC_SLIDER_AIMBOT_SMOOTH, "msctls_trackbar32", TBS_HORZ | TBS_AUTOTICKS | WS_TABSTOP, 210, 200, 160, 20
    LTEXT           "Strength: 80%", IDC_STATIC_STRENGTH, 210, 220, 80, 12, SS_LEFT
    CONTROL         "", IDC_SLIDER_AIMBOT_STRENGTH, "msctls_trackbar32", TBS_HORZ | TBS_AUTOTICKS | WS_TABSTOP, 210, 235, 160, 20
    
    // Equipment & Perks Group
    GROUPBOX        "Equipment & Perks", IDC_STATIC, 10, 210, 180, 100
    PUSHBUTTON      "Give All Perks", IDC_BTN_ALL_PERKS, 20, 230, 80, 25
    PUSHBUTTON      "Infinite Points", IDC_BTN_INFINITE_POINTS, 110, 230, 80, 25
    PUSHBUTTON      "Pack-a-Punch", IDC_BTN_PACK_PUNCH, 20, 265, 80, 25
    PUSHBUTTON      "Teleport to Spawn", IDC_BTN_TELEPORT_SPAWN, 110, 265, 80, 25
    
    // Weapon Management Group
    GROUPBOX        "Weapon Management", IDC_STATIC, 200, 190, 180, 100
    LTEXT           "Weapon ID:", IDC_STATIC, 210, 215, 50, 12, SS_LEFT
    EDITTEXT        IDC_EDIT_WEAPON_ID, 265, 212, 40, 14, ES_NUMBER
    PUSHBUTTON      "Give Weapon", IDC_BTN_GIVE_WEAPON, 315, 210, 60, 25
    LTEXT           "Common IDs:", IDC_STATIC, 210, 235, 50, 12, SS_LEFT
    LTEXT           "M1911: 1, Thompson: 3", IDC_STATIC, 210, 250, 120, 12, SS_LEFT
    LTEXT           "Wunderwaffe: 100", IDC_STATIC, 210, 265, 80, 12, SS_LEFT
    
    // Game Control Group
    GROUPBOX        "Game Control", IDC_STATIC, 10, 300, 370, 80
    PUSHBUTTON      "Advance Round", IDC_BTN_ADVANCE_ROUND, 20, 320, 80, 25
    AUTOCHECKBOX    "Freeze Round", IDC_CHK_FREEZE_ROUND, 110, 325, 80, 12
    
    // Instructions
    GROUPBOX        "Instructions", IDC_STATIC, 10, 390, 370, 80
    LTEXT           "1. Launch Call of Duty WWII and enter Zombies mode", IDC_STATIC, 20, 410, 200, 12, SS_LEFT
    LTEXT           "2. Click 'Attach to Game' to connect the trainer", IDC_STATIC, 20, 425, 200, 12, SS_LEFT
    LTEXT           "3. Use checkboxes and buttons to activate features", IDC_STATIC, 20, 440, 200, 12, SS_LEFT
    LTEXT           "4. Enjoy enhanced solo gameplay!", IDC_STATIC, 20, 455, 150, 12, SS_LEFT
END
