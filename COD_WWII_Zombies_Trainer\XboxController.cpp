#include "XboxController.h"
#include <algorithm>

XboxController::XboxController(DWORD index) : controllerIndex(index), connected(false) {
    ZeroMemory(&currentState, sizeof(XINPUT_STATE));
    ZeroMemory(&previousState, sizeof(XINPUT_STATE));
}

XboxController::~XboxController() {
    StopVibration();
}

bool XboxController::Update() {
    previousState = currentState;
    
    DWORD result = XInputGetState(controllerIndex, &currentState);
    connected = (result == ERROR_SUCCESS);
    
    return connected;
}

bool XboxController::IsButtonPressed(WORD button) const {
    return (currentState.Gamepad.wButtons & button) != 0;
}

bool XboxController::IsButtonJustPressed(WORD button) const {
    return (currentState.Gamepad.wButtons & button) != 0 && 
           (previousState.Gamepad.wButtons & button) == 0;
}

bool XboxController::IsButtonJustReleased(WORD button) const {
    return (currentState.Gamepad.wButtons & button) == 0 && 
           (previousState.Gamepad.wButtons & button) != 0;
}

float XboxController::GetLeftStickX() const {
    return NormalizeStick(currentState.Gamepad.sThumbLX, LEFT_THUMB_DEADZONE);
}

float XboxController::GetLeftStickY() const {
    return NormalizeStick(currentState.Gamepad.sThumbLY, LEFT_THUMB_DEADZONE);
}

float XboxController::GetRightStickX() const {
    return NormalizeStick(currentState.Gamepad.sThumbRX, RIGHT_THUMB_DEADZONE);
}

float XboxController::GetRightStickY() const {
    return NormalizeStick(currentState.Gamepad.sThumbRY, RIGHT_THUMB_DEADZONE);
}

float XboxController::GetLeftTrigger() const {
    return NormalizeTrigger(currentState.Gamepad.bLeftTrigger);
}

float XboxController::GetRightTrigger() const {
    return NormalizeTrigger(currentState.Gamepad.bRightTrigger);
}

void XboxController::SetVibration(float leftMotor, float rightMotor) {
    XINPUT_VIBRATION vibration;
    vibration.wLeftMotorSpeed = static_cast<WORD>(std::clamp(leftMotor, 0.0f, 1.0f) * 65535);
    vibration.wRightMotorSpeed = static_cast<WORD>(std::clamp(rightMotor, 0.0f, 1.0f) * 65535);
    
    XInputSetState(controllerIndex, &vibration);
}

void XboxController::StopVibration() {
    SetVibration(0.0f, 0.0f);
}

bool XboxController::IsAiming() const {
    return GetLeftTrigger() > TRIGGER_THRESHOLD;
}

bool XboxController::IsFiring() const {
    return GetRightTrigger() > TRIGGER_THRESHOLD;
}

bool XboxController::IsMoving() const {
    float x = GetLeftStickX();
    float y = GetLeftStickY();
    return (x * x + y * y) > 0.01f; // Small threshold to avoid noise
}

bool XboxController::IsLooking() const {
    float x = GetRightStickX();
    float y = GetRightStickY();
    return (x * x + y * y) > 0.01f; // Small threshold to avoid noise
}

float XboxController::ApplyDeadzone(float value, float deadzone) const {
    if (std::abs(value) < deadzone) {
        return 0.0f;
    }
    
    // Scale the value to account for deadzone
    float sign = (value > 0) ? 1.0f : -1.0f;
    float scaledValue = (std::abs(value) - deadzone) / (1.0f - deadzone);
    return sign * std::clamp(scaledValue, 0.0f, 1.0f);
}

float XboxController::NormalizeStick(SHORT value, float deadzone) const {
    float normalized = static_cast<float>(value) / 32767.0f;
    return ApplyDeadzone(normalized, deadzone);
}

float XboxController::NormalizeTrigger(BYTE value) const {
    float normalized = static_cast<float>(value) / 255.0f;
    return (normalized > TRIGGER_THRESHOLD) ? normalized : 0.0f;
}
