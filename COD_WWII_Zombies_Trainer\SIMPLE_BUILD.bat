@echo off
echo Building COD WWII Zombies Trainer...
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Must run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Create build directory
if not exist build mkdir build
cd build

REM Try to find Visual Studio
set "VS2022=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
set "VS2019=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"

if exist "%VS2022%" (
    call "%VS2022%"
    echo Using Visual Studio 2022
) else if exist "%VS2019%" (
    call "%VS2019%"
    echo Using Visual Studio 2019
) else (
    echo Visual Studio not found! Please install Visual Studio 2019 or 2022
    pause
    exit /b 1
)

REM Configure with CMake
cmake .. -G "Visual Studio 17 2022" -A x64
if %errorLevel% neq 0 (
    cmake .. -G "Visual Studio 16 2019" -A x64
    if %errorLevel% neq 0 (
        echo CMake failed! Make sure C<PERSON>ake is installed.
        pause
        exit /b 1
    )
)

REM Build the project
cmake --build . --config Release
if %errorLevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo SUCCESS! Trainer built successfully!
echo Location: build\Release\COD_WWII_Zombies_Trainer.exe
echo.
echo Next steps:
echo 1. Connect Xbox controller
echo 2. Launch COD WWII Zombies
echo 3. Run the trainer as Administrator
echo.
pause
