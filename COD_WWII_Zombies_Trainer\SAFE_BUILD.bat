@echo off
title COD WWII Zombies Trainer - Safe Build
color 0F

REM Create a log file so we can see what happened
set LOGFILE=build_log.txt
echo Build started at %date% %time% > %LOGFILE%

echo.
echo ========================================
echo COD WWII Zombies Trainer - Safe Build
echo ========================================
echo.
echo Creating log file: %LOGFILE%
echo All output will be saved to this file.
echo.

REM Check admin rights
echo Checking administrator rights... >> %LOGFILE%
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Not running as administrator >> %LOGFILE%
    echo.
    echo ERROR: Must run as Administrator!
    echo Right-click and select "Run as administrator"
    echo.
    echo Check %LOGFILE% for details.
    echo.
    echo Press ENTER to exit...
    set /p dummy=
    exit /b 1
) else (
    echo SUCCESS: Running as administrator >> %LOGFILE%
    echo ✓ Running as administrator
)

REM Check for source files
echo. >> %LOGFILE%
echo Checking source files... >> %LOGFILE%
if exist "main.cpp" (
    echo ✓ main.cpp found >> %LOGFILE%
    echo ✓ main.cpp found
) else (
    echo ERROR: main.cpp not found >> %LOGFILE%
    echo ✗ main.cpp not found
)

if exist "Trainer.cpp" (
    echo ✓ Trainer.cpp found >> %LOGFILE%
    echo ✓ Trainer.cpp found
) else (
    echo ERROR: Trainer.cpp not found >> %LOGFILE%
    echo ✗ Trainer.cpp not found
)

REM Check for Visual Studio
echo. >> %LOGFILE%
echo Checking Visual Studio... >> %LOGFILE%
set "VS2022=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
set "VS2019=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
set VSFOUND=0

if exist "%VS2022%" (
    echo ✓ Visual Studio 2022 found >> %LOGFILE%
    echo ✓ Visual Studio 2022 found
    set VSFOUND=1
    set VSVERSION=2022
    set VSPATH=%VS2022%
) else if exist "%VS2019%" (
    echo ✓ Visual Studio 2019 found >> %LOGFILE%
    echo ✓ Visual Studio 2019 found
    set VSFOUND=1
    set VSVERSION=2019
    set VSPATH=%VS2019%
) else (
    echo ERROR: Visual Studio not found >> %LOGFILE%
    echo ✗ Visual Studio not found
    echo.
    echo You need to install Visual Studio 2019 or 2022
    echo Download from: https://visualstudio.microsoft.com/downloads/
    echo Make sure to install "Desktop development with C++"
    echo.
    echo Check %LOGFILE% for details.
    echo.
    echo Press ENTER to exit...
    set /p dummy=
    exit /b 1
)

REM Create output directory
echo. >> %LOGFILE%
echo Creating output directory... >> %LOGFILE%
if not exist "bin" (
    mkdir bin >> %LOGFILE% 2>&1
    echo ✓ Created bin directory >> %LOGFILE%
    echo ✓ Created bin directory
) else (
    echo ✓ bin directory exists >> %LOGFILE%
    echo ✓ bin directory exists
)

REM Set up Visual Studio environment
echo. >> %LOGFILE%
echo Setting up Visual Studio %VSVERSION% environment... >> %LOGFILE%
echo ✓ Setting up Visual Studio %VSVERSION% environment...
call "%VSPATH%" >> %LOGFILE% 2>&1

REM Try to compile
echo. >> %LOGFILE%
echo Starting compilation... >> %LOGFILE%
echo ✓ Starting compilation...
echo.

cl.exe /EHsc /O2 /MT /I. main.cpp Memory.cpp Trainer.cpp XboxController.cpp /Fe:bin\COD_WWII_Zombies_Trainer.exe /link kernel32.lib user32.lib psapi.lib advapi32.lib xinput.lib >> %LOGFILE% 2>&1

if %errorLevel% equ 0 (
    echo. >> %LOGFILE%
    echo BUILD SUCCESSFUL! >> %LOGFILE%
    color 0A
    echo.
    echo ========================================
    echo BUILD SUCCESSFUL!
    echo ========================================
    echo.
    echo ✓ Trainer created: bin\COD_WWII_Zombies_Trainer.exe
    echo ✓ Log saved to: %LOGFILE%
    echo.
    echo READY TO USE:
    echo 1. Connect Xbox controller
    echo 2. Launch COD WWII Zombies
    echo 3. Right-click bin\COD_WWII_Zombies_Trainer.exe
    echo 4. Select "Run as administrator"
    echo.
) else (
    echo. >> %LOGFILE%
    echo BUILD FAILED! >> %LOGFILE%
    color 0C
    echo.
    echo ========================================
    echo BUILD FAILED!
    echo ========================================
    echo.
    echo ✗ Compilation failed
    echo ✓ Check %LOGFILE% for detailed error messages
    echo.
    echo Common solutions:
    echo - Make sure Visual Studio has C++ development tools
    echo - Try installing "Build Tools for Visual Studio"
    echo.
)

echo.
echo ========================================
echo WINDOW WILL NOT CLOSE
echo ========================================
echo.
echo You can now read everything above.
echo Check %LOGFILE% for complete details.
echo.
echo Press ENTER when you're done reading...
set /p dummy=
